# Called when player changes dimensions
# Shows available spawn points in the new dimension

# Only show message if there are spawns available
execute if data storage multispawn:data spawns[0] run tellraw @s [{"text":"[MultiSpawn] ","color":"green"},{"text":"Entered new dimension! ","color":"yellow"},{"text":"[View Spawns]","color":"blue","clickEvent":{"action":"run_command","value":"/function multispawn:menu/open"},"hoverEvent":{"action":"show_text","contents":"Click to see available spawn points"}}]
