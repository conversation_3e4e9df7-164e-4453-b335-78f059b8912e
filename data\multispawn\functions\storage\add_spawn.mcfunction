# Add a new spawn point to storage
# This function stores the current player's position and dimension

# Get current position and dimension
execute store result score #x ms_temp run data get entity @s Pos[0]
execute store result score #y ms_temp run data get entity @s Pos[1]
execute store result score #z ms_temp run data get entity @s Pos[2]

# Get dimension
data modify storage multispawn:temp dimension set from entity @s Dimension

# Increment spawn counter
scoreboard players add #global ms_spawn_count 1

# Store the spawn data
data modify storage multispawn:data spawns append value {id:0,name:"",x:0,y:0,z:0,dimension:""}
execute store result storage multispawn:data spawns[-1].id int 1 run scoreboard players get #global ms_spawn_count
execute store result storage multispawn:data spawns[-1].x int 1 run scoreboard players get #x ms_temp
execute store result storage multispawn:data spawns[-1].y int 1 run scoreboard players get #y ms_temp
execute store result storage multispawn:data spawns[-1].z int 1 run scoreboard players get #z ms_temp
data modify storage multispawn:data spawns[-1].dimension set from storage multispawn:temp dimension

# Set default name if none provided
execute unless data storage multispawn:temp name run data modify storage multispawn:temp name set value "Spawn Point"
data modify storage multispawn:data spawns[-1].name set from storage multispawn:temp name

# Clear temp storage
data remove storage multispawn:temp name
data remove storage multispawn:temp dimension
