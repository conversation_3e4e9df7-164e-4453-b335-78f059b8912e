# Teleport to custom dimension spawn point

execute store result storage multispawn:temp tp_x int 1 run scoreboard players get #tp_x ms_temp
execute store result storage multispawn:temp tp_y int 1 run scoreboard players get #tp_y ms_temp
execute store result storage multispawn:temp tp_z int 1 run scoreboard players get #tp_z ms_temp

# Set the dimension for the macro
data modify storage multispawn:temp tp_dimension set from storage multispawn:temp selected_spawn.dimension

# Use macro to teleport to the custom dimension
function multispawn:teleport/to_dimension_macro with storage multispawn:temp

tellraw @s [{"text":"[MultiSpawn] ","color":"green"},{"text":"Teleported to ","color":"white"},{"nbt":"selected_spawn.name","storage":"multispawn:temp","color":"yellow"},{"text":" in ","color":"white"},{"nbt":"selected_spawn.dimension","storage":"multispawn:temp","color":"yellow"}]
