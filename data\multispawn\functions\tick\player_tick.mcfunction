# Per-player tick function
# Detects various events and provides enhanced functionality

# Initialize player scores if they don't exist
execute unless score @s ms_last_death_count matches 0.. run scoreboard players set @s ms_last_death_count 0
execute unless score @s ms_last_dimension matches 0.. run scoreboard players set @s ms_last_dimension 0

# Death detection - check if player died and respawned
execute store result score #current_deaths ms_temp run scoreboard players get @s ms_death_count
execute if score #current_deaths ms_temp > @s ms_last_death_count run function multispawn:tick/on_death

# Dimension change detection
function multispawn:tick/check_dimension_change

# Auto-save spawn data periodically (every 5 minutes = 6000 ticks)
execute if score #global ms_temp matches 6000.. run function multispawn:tick/auto_save
