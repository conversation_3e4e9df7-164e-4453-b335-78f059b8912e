# Test function to verify datapack functionality

tellraw @s [{"text":"[MultiSpawn] ","color":"green"},{"text":"Testing datapack functionality...","color":"white"}]

# Test 1: Check if scoreboards exist
execute unless score #global ms_spawn_count matches 0.. run tellraw @s [{"text":"[Test] ","color":"red"},{"text":"ERROR: Scoreboards not initialized","color":"white"}]
execute if score #global ms_spawn_count matches 0.. run tellraw @s [{"text":"[Test] ","color":"green"},{"text":"✓ Scoreboards initialized","color":"white"}]

# Test 2: Check data storage
execute unless data storage multispawn:data spawns run tellraw @s [{"text":"[Test] ","color":"red"},{"text":"ERROR: Data storage not initialized","color":"white"}]
execute if data storage multispawn:data spawns run tellraw @s [{"text":"[Test] ","color":"green"},{"text":"✓ Data storage initialized","color":"white"}]

# Test 3: Show current spawn count
execute store result score #test_count ms_temp run scoreboard players get #global ms_spawn_count
tellraw @s [{"text":"[Test] ","color":"blue"},{"text":"Current spawn count: ","color":"white"},{"score":{"name":"#test_count","objective":"ms_temp"},"color":"yellow"}]

# Test 4: Show current dimension
tellraw @s [{"text":"[Test] ","color":"blue"},{"text":"Current dimension: ","color":"white"},{"nbt":"Dimension","entity":"@s","color":"yellow"}]

tellraw @s [{"text":"[MultiSpawn] ","color":"green"},{"text":"Test completed!","color":"white"}]
