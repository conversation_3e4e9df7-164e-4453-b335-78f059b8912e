# Add a spawn point at the current player location with default name
# This is the main user-facing function for adding spawns

# Set default name
data modify storage multispawn:temp name set value "Spawn Point"

# Add the spawn
function multispawn:storage/add_spawn

# Notify the player
execute store result score #spawn_id ms_temp run scoreboard players get #global ms_spawn_count
tellraw @s [{"text":"[MultiSpawn] ","color":"green"},{"text":"Added spawn point #","color":"white"},{"score":{"name":"#spawn_id","objective":"ms_temp"},"color":"yellow"},{"text":" at your current location","color":"white"}]

# Show current dimension
tellraw @s [{"text":"[MultiSpawn] ","color":"green"},{"text":"Dimension: ","color":"white"},{"nbt":"Dimension","entity":"@s","color":"yellow"}]
