# Remove a spawn point by ID
# Usage: Set #target_id ms_temp to the ID you want to remove

# Store the target ID
execute store result score #target ms_temp run data get storage multispawn:temp target_id

# Create a new list without the target spawn
data modify storage multispawn:temp new_spawns set value []

# Copy all spawns except the target
function multispawn:storage/filter_spawns

# Replace the original list
data modify storage multispawn:data spawns set from storage multispawn:temp new_spawns

# Clean up
data remove storage multispawn:temp new_spawns
data remove storage multispawn:temp target_id
