# MultiSpawn Datapack Load Function
# Initializes the datapack and sets up necessary scoreboards

tellraw @a {"text":"[MultiSpawn] Datapack loaded successfully!","color":"green"}

# Create scoreboards for managing spawn points
scoreboard objectives add ms_spawn_count dummy "Spawn Count"
scoreboard objectives add ms_temp dummy "Temporary Values"
scoreboard objectives add ms_menu_page dummy "Menu Page"

# Initialize spawn counter
scoreboard players set #global ms_spawn_count 0

# Set up data storage
data merge storage multispawn:data {spawns:[]}

tellraw @a {"text":"[MultiSpawn] Use /function multispawn:help for commands","color":"yellow"}
