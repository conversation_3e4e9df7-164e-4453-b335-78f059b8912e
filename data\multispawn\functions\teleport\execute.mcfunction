# Execute the teleportation to the selected spawn point

# Get coordinates from storage
execute store result score #tp_x ms_temp run data get storage multispawn:temp selected_spawn.x
execute store result score #tp_y ms_temp run data get storage multispawn:temp selected_spawn.y
execute store result score #tp_z ms_temp run data get storage multispawn:temp selected_spawn.z

# Store dimension for teleportation
data modify storage multispawn:temp tp_dimension set from storage multispawn:temp selected_spawn.dimension

# Notify player before teleportation
tellraw @s [{"text":"[MultiSpawn] ","color":"green"},{"text":"Teleporting to ","color":"white"},{"nbt":"selected_spawn.name","storage":"multispawn:temp","color":"yellow"},{"text":"...","color":"white"}]

# Execute teleportation based on dimension
execute if data storage multispawn:temp {tp_dimension:"minecraft:overworld"} run function multispawn:teleport/to_overworld
execute if data storage multispawn:temp {tp_dimension:"minecraft:the_nether"} run function multispawn:teleport/to_nether
execute if data storage multispawn:temp {tp_dimension:"minecraft:the_end"} run function multispawn:teleport/to_end

# Handle custom dimensions (any dimension not matching the above)
execute unless data storage multispawn:temp {tp_dimension:"minecraft:overworld"} unless data storage multispawn:temp {tp_dimension:"minecraft:the_nether"} unless data storage multispawn:temp {tp_dimension:"minecraft:the_end"} run function multispawn:teleport/to_custom_dimension

# Clean up temp data
data remove storage multispawn:temp selected_spawn
data remove storage multispawn:temp tp_dimension
