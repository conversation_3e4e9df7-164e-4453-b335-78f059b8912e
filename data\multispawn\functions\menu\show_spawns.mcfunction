# Display the spawn selection menu with all available spawns

# Header
tellraw @s {"text":"","extra":[{"text":"=== ","color":"gold"},{"text":"MultiSpawn Menu","color":"yellow","bold":true},{"text":" ===","color":"gold"}]}
tellraw @s {"text":""}
tellraw @s {"text":"Click on a spawn point to teleport:","color":"white"}
tellraw @s {"text":""}

# Copy spawns to temp storage for processing
data modify storage multispawn:temp menu_spawns set from storage multispawn:data spawns

# Display each spawn
function multispawn:menu/display_spawn_list

# Footer with options
tellraw @s {"text":""}
tellraw @s [{"text":"[","color":"gray"},{"text":"Add New Spawn","color":"green","clickEvent":{"action":"run_command","value":"/function multispawn:add_spawn_here"},"hoverEvent":{"action":"show_text","contents":"Add a spawn point at your current location"}},{"text":"]","color":"gray"},{"text":" ","color":"white"},{"text":"[","color":"gray"},{"text":"Refresh Menu","color":"blue","clickEvent":{"action":"run_command","value":"/function multispawn:menu/open"},"hoverEvent":{"action":"show_text","contents":"Refresh the spawn menu"}},{"text":"]","color":"gray"}]
tellraw @s {"text":""}
tellraw @s {"text":"===================","color":"gold"}

# Clean up temp storage
data remove storage multispawn:temp menu_spawns
