# Check if player changed dimensions
# Shows relevant spawn points when entering a new dimension

# Get current dimension hash (simple hash for comparison)
execute store result score #current_dim ms_temp run data get entity @s Dimension

# Check if dimension changed
execute unless score #current_dim ms_temp = @s ms_last_dimension run function multispawn:tick/on_dimension_change

# Update last dimension
scoreboard players operation @s ms_last_dimension = #current_dim ms_temp
