# Check if current spawn matches the target ID

# Get current spawn ID
execute store result score #current_id ms_temp run data get storage multispawn:temp search_spawns[0].id

# If this is the target spawn, teleport to it
execute if score #current_id ms_temp = #spawn_id ms_temp run data modify storage multispawn:temp selected_spawn set from storage multispawn:temp search_spawns[0]
execute if score #current_id ms_temp = #spawn_id ms_temp run function multispawn:teleport/execute

# Remove processed spawn and continue searching
data remove storage multispawn:temp search_spawns[0]
execute unless score #current_id ms_temp = #spawn_id ms_temp if data storage multispawn:temp search_spawns[0] run function multispawn:teleport/check_spawn_id

# Clean up
execute unless data storage multispawn:temp search_spawns[0] run data remove storage multispawn:temp search_spawns
