# Process a single spawn for filtering
# Copy spawn to new list if it's not the target

# Get the current spawn's ID
execute store result score #current ms_temp run data get storage multispawn:data spawns[0].id

# If this spawn's ID doesn't match the target, keep it
execute unless score #current ms_temp = #target ms_temp run data modify storage multispawn:temp new_spawns append from storage multispawn:data spawns[0]

# Remove the processed spawn from the original list
data remove storage multispawn:data spawns[0]

# Continue processing if there are more spawns
execute if data storage multispawn:data spawns[0] run function multispawn:storage/process_spawn
