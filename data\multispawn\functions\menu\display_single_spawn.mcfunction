# Display a single spawn point in the menu

# Get spawn data
data modify storage multispawn:temp current_spawn set from storage multispawn:temp menu_spawns[0]

# Store current spawn for teleportation (before creating the clickable text)
data modify storage multispawn:temp selected_spawn set from storage multispawn:temp current_spawn

# Create the clickable spawn entry
tellraw @s [{"text":"• ","color":"gray"},{"nbt":"current_spawn.name","storage":"multispawn:temp","color":"yellow","clickEvent":{"action":"run_command","value":"/function multispawn:teleport/prepare_from_menu"},"hoverEvent":{"action":"show_text","contents":[{"text":"Click to teleport to ","color":"white"},{"nbt":"current_spawn.name","storage":"multispawn:temp","color":"yellow"},{"text":"\\nLocation: ","color":"gray"},{"nbt":"current_spawn.x","storage":"multispawn:temp","color":"white"},{"text":", ","color":"white"},{"nbt":"current_spawn.y","storage":"multispawn:temp","color":"white"},{"text":", ","color":"white"},{"nbt":"current_spawn.z","storage":"multispawn:temp","color":"white"},{"text":"\\nDimension: ","color":"gray"},{"nbt":"current_spawn.dimension","storage":"multispawn:temp","color":"white"}]}},{"text":" (ID: ","color":"gray"},{"nbt":"current_spawn.id","storage":"multispawn:temp","color":"white"},{"text":")","color":"gray"}]

# Remove the processed spawn
data remove storage multispawn:temp menu_spawns[0]

# Continue with next spawn if available
execute if data storage multispawn:temp menu_spawns[0] run function multispawn:menu/display_single_spawn
