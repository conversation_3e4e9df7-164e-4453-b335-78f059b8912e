# MultiSpawn Datapack

A Minecraft datapack that allows you to create multiple spawn points across different dimensions with an interactive menu system. Works without cheats enabled!

## Features

- ✅ Create unlimited spawn points in any dimension
- ✅ Interactive clickable menu for spawn selection
- ✅ Works across all dimensions (Overworld, Nether, End, and custom dimensions)
- ✅ No cheats required - works in survival mode
- ✅ Persistent storage - spawns are saved between sessions
- ✅ Easy-to-use commands
- ✅ Smart detection - auto-suggests spawns on death and dimension changes
- ✅ Background monitoring - continuous tick-based event detection

## Installation

1. Download or clone this datapack
2. Place the entire folder in your world's `datapacks` folder
3. Reload datapacks with `/reload` or restart your world
4. You should see a green message confirming the datapack loaded

## Commands

### Main Commands
- `/function multispawn:menu` - Open the spawn selection menu
- `/function multispawn:add` - Add a spawn point at your current location
- `/function multispawn:help` - Show help information

### Admin Commands
- `/function multispawn:reset` - Delete all spawn points (requires confirmation)
- `/function multispawn:test` - Test datapack functionality

## How to Use

1. **Adding Spawn Points:**
   - Go to the location where you want to create a spawn point
   - Run `/function multispawn:add`
   - The spawn point will be saved with your current coordinates and dimension

2. **Using Spawn Points:**
   - Run `/function multispawn:menu` to open the spawn selection menu
   - Click on any spawn point name to teleport there instantly
   - The menu shows spawn ID, coordinates, and dimension information

3. **Managing Spawn Points:**
   - Use the menu to see all your spawn points
   - Use `/function multispawn:reset` to delete all spawns (admin only)

## Technical Details

- **Pack Format:** 72 (Minecraft 1.21.5)
- **Storage:** Uses data storage and scoreboards for persistence
- **Compatibility:** Works with Fabric 1.21.5 and vanilla Minecraft
- **No Cheats:** Uses only functions and data commands that work without cheats

## File Structure

```
MultiSpawn/
├── pack.mcmeta
├── data/
│   ├── minecraft/tags/functions/
│   │   ├── load.json
│   │   └── unload.json
│   └── multispawn/functions/
│       ├── load.mcfunction
│       ├── unload.mcfunction
│       ├── help.mcfunction
│       ├── menu.mcfunction
│       ├── add.mcfunction
│       ├── storage/
│       ├── menu/
│       └── teleport/
```

## Troubleshooting

1. **Datapack not loading:**
   - Make sure the folder is in the correct `datapacks` directory
   - Run `/reload` to reload datapacks
   - Check that you have the correct Minecraft version (1.21.5)

2. **Spawns not working:**
   - Run `/function multispawn:test` to check functionality
   - Make sure you're not in spectator mode
   - Verify the spawn was added with `/function multispawn:menu`

3. **Menu not showing:**
   - Make sure you have spawns created first
   - Try running `/function multispawn:help` to verify the datapack is loaded

## License

This datapack is free to use and modify. Created for Minecraft Fabric 1.21.5.
