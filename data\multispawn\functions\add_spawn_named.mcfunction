# Add a spawn point with a custom name
# Usage: Set the name in multispawn:temp name before calling this function

# Check if name is provided
execute unless data storage multispawn:temp name run data modify storage multispawn:temp name set value "Custom Spawn"

# Add the spawn
function multispawn:storage/add_spawn

# Notify the player
execute store result score #spawn_id ms_temp run scoreboard players get #global ms_spawn_count
tellraw @s [{"text":"[MultiSpawn] ","color":"green"},{"text":"Added spawn point \"","color":"white"},{"nbt":"spawns[-1].name","storage":"multispawn:data","color":"yellow"},{"text":"\" (#","color":"white"},{"score":{"name":"#spawn_id","objective":"ms_temp"},"color":"yellow"},{"text":") at your current location","color":"white"}]

# Show current dimension
tellraw @s [{"text":"[MultiSpawn] ","color":"green"},{"text":"Dimension: ","color":"white"},{"nbt":"Dimension","entity":"@s","color":"yellow"}]
